# 批量更新剩余云函数
$remainingFunctions = @(
    'addTimeSlot', 'applyWeeklyTemplate', 'coachRegister', 'createCourse', 
    'deleteCourse', 'deleteTimeSlot', 'getCityList', 'getCoachAppointments',
    'getCoachCourses', 'getCoachDashboard', 'getCoachDaySchedule', 
    'getCoachReviews', 'getCoachSchedule', 'initCoachCollections',
    'initDatabase', 'migrateCoachData', 'searchCoachesOptimized',
    'submitFeedback', 'updateAppointmentStatus', 'updateCoachProfile',
    'updateCourse', 'updateCourseStatus', 'updateTimeSlot', 'uploadDefaultImages'
)

foreach ($funcName in $remainingFunctions) {
    $packagePath = "cloudfunctions\$funcName\package.json"
    if (Test-Path $packagePath) {
        Write-Host "Processing $funcName..."
        
        # 读取当前内容
        $content = Get-Content $packagePath -Raw
        
        # 检查是否已经配置了依赖层
        if ($content -notmatch 'cloudbaseFunction') {
            # 替换 wx-server-sdk 依赖为依赖层配置
            $newContent = $content -replace '"dependencies":\s*\{[^}]*"wx-server-sdk"[^}]*\}', '"dependencies": {}'
            $newContent = $newContent -replace '\}(\s*)$', (',
  "cloudbaseFunction": {
    "layers": [
      {
        "name": "wx-server-sdk-layer",
        "version": "1"
      }
    ]
  }
}$1')
            
            Set-Content $packagePath $newContent -Encoding UTF8
            Write-Host "✓ Updated $funcName"
        } else {
            Write-Host "○ $funcName already configured"
        }
    }
}

Write-Host "Batch update completed!"
