# 批量更新云函数配置使用依赖层
$cloudFunctionsPath = "cloudfunctions"
$excludeDirs = @("layers")

Get-ChildItem $cloudFunctionsPath -Directory | Where-Object { $_.Name -notin $excludeDirs } | ForEach-Object {
    $functionName = $_.Name
    $packageJsonPath = "$($_.FullName)\package.json"
    
    if (Test-Path $packageJsonPath) {
        Write-Host "Updating $functionName..."
        
        try {
            $content = Get-Content $packageJsonPath -Raw | ConvertFrom-Json
            
            # 移除 wx-server-sdk 依赖
            if ($content.dependencies -and $content.dependencies.'wx-server-sdk') {
                $content.dependencies.PSObject.Properties.Remove('wx-server-sdk')
            }
            
            # 如果 dependencies 为空，设置为空对象
            if (-not $content.dependencies -or $content.dependencies.PSObject.Properties.Count -eq 0) {
                $content.dependencies = [PSCustomObject]@{}
            }
            
            # 添加依赖层配置
            $content | Add-Member -MemberType NoteProperty -Name "cloudbaseFunction" -Value ([PSCustomObject]@{
                layers = @(
                    [PSCustomObject]@{
                        name = "wx-server-sdk-layer"
                        version = "1"
                    }
                )
            }) -Force
            
            # 保存文件
            $content | ConvertTo-Json -Depth 10 | Set-Content $packageJsonPath -Encoding UTF8
            Write-Host "✓ Updated $functionName"
            
        } catch {
            Write-Host "✗ Failed to update $functionName : $($_.Exception.Message)"
        }
    }
}

Write-Host "All cloud functions updated!"
